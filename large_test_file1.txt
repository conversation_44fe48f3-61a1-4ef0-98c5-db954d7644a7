// Large Test File 1 - Original Code
function calculateSum(numbers) {
    let sum = 0;
    for (let i = 0; i < numbers.length; i++) {
        sum += numbers[i];
    }
    return sum;
}

function calculateAverage(numbers) {
    if (numbers.length === 0) {
        return 0;
    }
    const sum = calculateSum(numbers);
    return sum / numbers.length;
}

function findMaximum(numbers) {
    if (numbers.length === 0) {
        return null;
    }
    let max = numbers[0];
    for (let i = 1; i < numbers.length; i++) {
        if (numbers[i] > max) {
            max = numbers[i];
        }
    }
    return max;
}

function findMinimum(numbers) {
    if (numbers.length === 0) {
        return null;
    }
    let min = numbers[0];
    for (let i = 1; i < numbers.length; i++) {
        if (numbers[i] < min) {
            min = numbers[i];
        }
    }
    return min;
}

function sortNumbers(numbers, ascending = true) {
    const sorted = [...numbers];
    if (ascending) {
        return sorted.sort((a, b) => a - b);
    } else {
        return sorted.sort((a, b) => b - a);
    }
}

function filterEvenNumbers(numbers) {
    return numbers.filter(num => num % 2 === 0);
}

function filterOddNumbers(numbers) {
    return numbers.filter(num => num % 2 !== 0);
}

function multiplyByTwo(numbers) {
    return numbers.map(num => num * 2);
}

function squareNumbers(numbers) {
    return numbers.map(num => num * num);
}

function sumOfSquares(numbers) {
    const squares = squareNumbers(numbers);
    return calculateSum(squares);
}

// Test data
const testNumbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
const moreNumbers = [15, 22, 8, 33, 41, 7, 19, 28, 36, 44];

console.log("Sum:", calculateSum(testNumbers));
console.log("Average:", calculateAverage(testNumbers));
console.log("Maximum:", findMaximum(testNumbers));
console.log("Minimum:", findMinimum(testNumbers));
console.log("Sorted ascending:", sortNumbers(testNumbers));
console.log("Sorted descending:", sortNumbers(testNumbers, false));
console.log("Even numbers:", filterEvenNumbers(testNumbers));
console.log("Odd numbers:", filterOddNumbers(testNumbers));
console.log("Multiplied by 2:", multiplyByTwo(testNumbers));
console.log("Squared:", squareNumbers(testNumbers));
console.log("Sum of squares:", sumOfSquares(testNumbers));

// Additional utility functions
function removeDuplicates(numbers) {
    return [...new Set(numbers)];
}

function getRandomNumber(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function generateRandomArray(length, min = 1, max = 100) {
    const array = [];
    for (let i = 0; i < length; i++) {
        array.push(getRandomNumber(min, max));
    }
    return array;
}

function calculateMedian(numbers) {
    const sorted = sortNumbers(numbers);
    const middle = Math.floor(sorted.length / 2);
    
    if (sorted.length % 2 === 0) {
        return (sorted[middle - 1] + sorted[middle]) / 2;
    } else {
        return sorted[middle];
    }
}

function calculateMode(numbers) {
    const frequency = {};
    let maxFreq = 0;
    let modes = [];
    
    for (const num of numbers) {
        frequency[num] = (frequency[num] || 0) + 1;
        if (frequency[num] > maxFreq) {
            maxFreq = frequency[num];
        }
    }
    
    for (const num in frequency) {
        if (frequency[num] === maxFreq) {
            modes.push(Number(num));
        }
    }
    
    return modes;
}

function calculateRange(numbers) {
    const max = findMaximum(numbers);
    const min = findMinimum(numbers);
    return max - min;
}

function calculateVariance(numbers) {
    const mean = calculateAverage(numbers);
    const squaredDifferences = numbers.map(num => Math.pow(num - mean, 2));
    return calculateAverage(squaredDifferences);
}

function calculateStandardDeviation(numbers) {
    return Math.sqrt(calculateVariance(numbers));
}

// More test cases
const largeDataset = generateRandomArray(1000, 1, 1000);
console.log("Large dataset statistics:");
console.log("Count:", largeDataset.length);
console.log("Sum:", calculateSum(largeDataset));
console.log("Average:", calculateAverage(largeDataset));
console.log("Median:", calculateMedian(largeDataset));
console.log("Mode:", calculateMode(largeDataset));
console.log("Range:", calculateRange(largeDataset));
console.log("Variance:", calculateVariance(largeDataset));
console.log("Standard Deviation:", calculateStandardDeviation(largeDataset));

// Performance testing
console.time("Large calculation");
for (let i = 0; i < 10000; i++) {
    calculateSum(testNumbers);
}
console.timeEnd("Large calculation");

// Edge cases
console.log("Empty array sum:", calculateSum([]));
console.log("Empty array average:", calculateAverage([]));
console.log("Empty array max:", findMaximum([]));
console.log("Empty array min:", findMinimum([]));

// Complex operations
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

function isPrime(num) {
    if (num < 2) return false;
    for (let i = 2; i <= Math.sqrt(num); i++) {
        if (num % i === 0) return false;
    }
    return true;
}

function getPrimes(max) {
    const primes = [];
    for (let i = 2; i <= max; i++) {
        if (isPrime(i)) {
            primes.push(i);
        }
    }
    return primes;
}

console.log("First 10 Fibonacci numbers:");
for (let i = 0; i < 10; i++) {
    console.log(`F(${i}) = ${fibonacci(i)}`);
}

console.log("Prime numbers up to 50:", getPrimes(50));

// Final summary
console.log("\n=== SUMMARY ===");
console.log("This file contains various mathematical utility functions");
console.log("for working with arrays of numbers, including:");
console.log("- Basic operations (sum, average, min, max)");
console.log("- Sorting and filtering");
console.log("- Statistical calculations");
console.log("- Performance testing");
console.log("- Edge case handling");
console.log("Total lines of code: ~200");
