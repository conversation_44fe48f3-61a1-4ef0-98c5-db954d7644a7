<!DOCTYPE html>
<html>
<head>
    <title>Test Diff Functionality</title>
</head>
<body>
    <h1>Testing Diff Functionality</h1>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/diff/5.1.0/diff.min.js"></script>
    
    <script>
        console.log('Testing diff library...');
        
        // Test if library loads
        setTimeout(() => {
            console.log('window.Diff:', typeof window.Diff);
            console.log('Diff:', typeof Diff);
            console.log('JsDiff:', typeof JsDiff);
            
            if (window.Diff) {
                try {
                    const result = window.Diff.diffLines('Hello\nWorld', 'Hello\nUniverse');
                    console.log('Diff test result:', result);
                } catch (e) {
                    console.error('Diff test error:', e);
                }
            }
        }, 1000);
    </script>
</body>
</html>
