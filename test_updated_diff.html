<!DOCTYPE html>
<html>
<head>
    <title>Test Updated Diff</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Testing Updated Diff Checker</h1>
    <div id="results"></div>
    
    <script src="https://unpkg.com/diff@5.1.0/dist/diff.min.js"></script>
    <script>
        const results = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }
        
        // Test 1: Check if library loads
        setTimeout(() => {
            addResult('Testing diff library loading...', 'info');
            
            if (typeof window.Diff !== 'undefined') {
                addResult('✓ window.Diff is available', 'success');
                
                // Test 2: Basic diff functionality
                try {
                    const result = window.Diff.diffLines('Hello\nWorld', 'Hello\nUniverse');
                    addResult('✓ Basic diff test successful', 'success');
                    addResult(`Diff result: ${JSON.stringify(result, null, 2)}`, 'info');
                    
                    // Test 3: Word diff functionality
                    const wordResult = window.Diff.diffWords('Hello World', 'Hello Universe');
                    addResult('✓ Word diff test successful', 'success');
                    addResult(`Word diff result: ${JSON.stringify(wordResult, null, 2)}`, 'info');
                    
                } catch (e) {
                    addResult(`✗ Diff functionality test failed: ${e.message}`, 'error');
                }
            } else {
                addResult('✗ window.Diff is not available', 'error');
            }
            
            // Test the updated diff checker
            addResult('Opening updated diff checker...', 'info');
            setTimeout(() => {
                window.open('diffchecker.html', '_blank');
            }, 1000);
            
        }, 1000);
    </script>
</body>
</html>
