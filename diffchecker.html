<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intelligent Diff Checker Tool</title>
    
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- jsdiff library for the core diffing logic -->
    <script src="https://unpkg.com/diff@5.1.0/dist/diff.min.js" onload="console.log('jsdiff library loaded')" onerror="console.error('Failed to load jsdiff library from unpkg')"></script>
    <!-- Fallback CDN -->
    <script>
        // Fallback if unpkg fails
        if (typeof window.Diff === 'undefined') {
            console.log('Loading fallback diff library...');
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/diff@5.1.0/dist/diff.min.js';
            script.onload = () => console.log('Fallback diff library loaded');
            script.onerror = () => console.error('Fallback diff library failed to load');
            document.head.appendChild(script);
        }
    </script>
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet">

    <style>
        /* Custom styles for the application */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc; /* slate-50 */
        }
        .diff-font {
            font-family: 'Fira Code', monospace;
        }
        /* Custom scrollbar for a cleaner look */
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9; /* slate-100 */
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1; /* slate-300 */
            border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8; /* slate-400 */
        }
        /* Line highlight styles */
        .line {
            display: flex;
            min-height: 1.5rem; /* Ensures empty lines are visible */
            line-height: 1.5rem;
            padding: 0 0.5rem;
        }
        .line-num {
            width: 40px;
            min-width: 40px;
            text-align: right;
            padding-right: 10px;
            color: #64748b; /* slate-500 */
            border-right: 1px solid #e2e8f0; /* slate-200 */
            -webkit-user-select: none; /* Safari */
            -ms-user-select: none; /* IE 10+ */
            user-select: none;
        }
        .line-content {
            padding-left: 10px;
            white-space: pre-wrap;
            word-break: break-all;
            flex-grow: 1;
        }
        .line-added {
            background-color: #ddfbe8; /* A slightly softer green */
        }
        .line-removed {
            background-color: #fee2e2; /* A slightly softer red */
        }
        .line-modified-word {
            background-color: #bfdbfe; /* blue-200 */
            border-radius: 3px;
            padding: 1px 0;
        }
        .line-blank {
            background-color: #f1f5f9; /* slate-100 */
        }
        /* Disable resize handle on textareas */
        textarea {
            resize: none;
        }
        /* Custom notification styles */
        .notification {
            transition: all 0.5s ease-in-out;
            opacity: 0;
            transform: translateX(100%);
            max-width: 350px;
        }
        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }
    </style>
</head>
<body class="text-slate-800">

    <div class="container mx-auto p-4 md:p-6 lg:p-8">
        <!-- Header Section -->
        <header class="mb-6 text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-slate-900">Intelligent Diff Checker</h1>
            <p class="mt-2 text-slate-600">Compare text, files, and code with advanced highlighting.</p>
        </header>

        <!-- Controls and Options Section -->
        <div class="bg-white p-4 rounded-xl shadow-md mb-6">
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 items-center">
                <!-- Main Action Button -->
                <div class="col-span-2 sm:col-span-3 md:col-span-1">
                    <button id="compare-btn" class="w-full bg-blue-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 disabled:bg-slate-400 disabled:cursor-not-allowed">
                        Compare
                    </button>
                </div>
                <!-- Toggle Options -->
                <div class="col-span-2 sm:col-span-3 md:col-span-3 lg:col-span-5 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-x-4 gap-y-2">
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" id="show-line-numbers" class="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" checked>
                        <span class="text-sm text-slate-700">Line Numbers</span>
                    </label>
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" id="show-blank-lines" class="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" checked>
                        <span class="text-sm text-slate-700">Show Blank Lines</span>
                    </label>
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" id="ignore-whitespace" class="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                        <span class="text-sm text-slate-700">Ignore Whitespace</span>
                    </label>
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" id="case-sensitive" class="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" checked>
                        <span class="text-sm text-slate-700">Case Sensitive</span>
                    </label>
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" id="word-level-diff" class="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" checked>
                        <span class="text-sm text-slate-700">Word-Level Diff</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Input Panels Section -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Left Panel -->
            <div class="bg-white rounded-xl shadow-md p-1">
                <div class="flex justify-between items-center p-3 border-b border-slate-200">
                    <h2 class="font-semibold text-lg">Original Text</h2>
                    <div>
                        <input type="file" id="file-input-1" class="hidden" accept=".txt,.js,.html,.css,.md,.json">
                        <button onclick="document.getElementById('file-input-1').click()" class="text-sm bg-slate-100 hover:bg-slate-200 text-slate-700 font-medium py-1 px-3 rounded-md transition-colors">Upload File</button>
                    </div>
                </div>
                <textarea id="text-input-1" class="w-full h-64 p-3 diff-font text-sm border-0 focus:ring-0 custom-scrollbar" placeholder="Paste original text here...">Hello World
This is line 2
This is line 3
Common line</textarea>
            </div>
            <!-- Right Panel -->
            <div class="bg-white rounded-xl shadow-md p-1">
                 <div class="flex justify-between items-center p-3 border-b border-slate-200">
                    <h2 class="font-semibold text-lg">Changed Text</h2>
                    <div>
                        <input type="file" id="file-input-2" class="hidden" accept=".txt,.js,.html,.css,.md,.json">
                        <button onclick="document.getElementById('file-input-2').click()" class="text-sm bg-slate-100 hover:bg-slate-200 text-slate-700 font-medium py-1 px-3 rounded-md transition-colors">Upload File</button>
                    </div>
                </div>
                <textarea id="text-input-2" class="w-full h-64 p-3 diff-font text-sm border-0 focus:ring-0 custom-scrollbar" placeholder="Paste changed text here...">Hello Universe
This is line 2 modified
This is a new line 3
Common line
New line added</textarea>
            </div>
        </div>

        <!-- Diff Results Section -->
        <div id="results-container" class="hidden">
            <!-- Stats and Legend -->
            <div class="bg-white p-4 rounded-xl shadow-md mb-6 flex flex-wrap justify-between items-center gap-4">
                <div id="stats-display" class="flex flex-wrap gap-x-4 gap-y-2 text-sm font-medium">
                    <!-- Stats will be injected here -->
                </div>
                <div class="flex flex-wrap gap-x-4 gap-y-2 text-sm">
                    <div class="flex items-center"><span class="w-3 h-3 rounded-full bg-green-200 mr-2"></span>Added</div>
                    <div class="flex items-center"><span class="w-3 h-3 rounded-full bg-red-200 mr-2"></span>Deleted</div>
                    <div class="flex items-center"><span class="w-3 h-3 rounded-full bg-blue-200 mr-2"></span>Modified Word</div>
                    <div class="flex items-center"><span class="w-3 h-3 rounded-full bg-slate-100 mr-2"></span>Blank Line</div>
                </div>
            </div>

            <!-- Output Panels -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 diff-font text-sm">
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="p-3 font-semibold border-b border-slate-200 bg-slate-50">Original</div>
                    <div id="diff-output-1" class="overflow-auto custom-scrollbar" style="max-height: 60vh;"></div>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="p-3 font-semibold border-b border-slate-200 bg-slate-50">Changed</div>
                    <div id="diff-output-2" class="overflow-auto custom-scrollbar" style="max-height: 60vh;"></div>
                </div>
            </div>
            
            <!-- Action Buttons for Results -->
            <div class="mt-6 flex justify-center gap-4">
                <button id="copy-btn" class="bg-slate-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-slate-700 transition-colors">Copy Diff</button>
                <button id="clear-btn" class="bg-red-500 text-white font-bold py-2 px-6 rounded-lg hover:bg-red-600 transition-colors">Clear All</button>
                <button id="test-btn" class="bg-green-500 text-white font-bold py-2 px-6 rounded-lg hover:bg-green-600 transition-colors">Test Library</button>
            </div>
        </div>
        
        <!-- Initial state message -->
        <div id="initial-message" class="text-center py-10 px-6 bg-white rounded-xl shadow-md">
            <h3 class="text-xl font-semibold text-slate-700">Ready to Compare</h3>
            <p class="text-slate-500 mt-2">Paste your text into the panels above and click the "Compare" button to see the differences.</p>
        </div>
    </div>

    <!-- Notification container for custom alerts -->
    <div id="notification-container" class="fixed top-5 right-5 z-50 flex flex-col items-end gap-2"></div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM Content Loaded - Initializing Diff Checker');
            // DOM element references
            const textInput1 = document.getElementById('text-input-1');
            const textInput2 = document.getElementById('text-input-2');
            const fileInput1 = document.getElementById('file-input-1');
            const fileInput2 = document.getElementById('file-input-2');
            const compareBtn = document.getElementById('compare-btn');
            const clearBtn = document.getElementById('clear-btn');
            const copyBtn = document.getElementById('copy-btn');
            const testBtn = document.getElementById('test-btn');
            const diffOutput1 = document.getElementById('diff-output-1');
            const diffOutput2 = document.getElementById('diff-output-2');
            const resultsContainer = document.getElementById('results-container');
            const initialMessage = document.getElementById('initial-message');
            const statsDisplay = document.getElementById('stats-display');
            const notificationContainer = document.getElementById('notification-container');

            // Toggle options
            const showLineNumbersCheck = document.getElementById('show-line-numbers');
            const showBlankLinesCheck = document.getElementById('show-blank-lines');
            const ignoreWhitespaceCheck = document.getElementById('ignore-whitespace');
            const caseSensitiveCheck = document.getElementById('case-sensitive');
            const wordLevelDiffCheck = document.getElementById('word-level-diff');

            // --- FIX: Wait for jsdiff library to load before enabling the compare button ---
            compareBtn.disabled = true;
            compareBtn.textContent = 'Loading Tool...';

            let checkCount = 0;
            const maxChecks = 50; // 5 seconds timeout
            const readyInterval = setInterval(() => {
                checkCount++;
                // Check for both possible ways the library might be exposed
                if (typeof window.Diff !== 'undefined' || typeof Diff !== 'undefined' || typeof JsDiff !== 'undefined') {
                    clearInterval(readyInterval);
                    compareBtn.disabled = false;
                    compareBtn.textContent = 'Compare';
                    console.log('Diff library loaded successfully');

                    // Test the library
                    try {
                        const DiffLib = window.Diff || Diff || JsDiff;
                        const testDiff = DiffLib.diffLines('test1', 'test2');
                        console.log('Diff library test successful:', testDiff);
                    } catch (e) {
                        console.warn('Diff library test failed:', e);
                    }
                } else if (checkCount >= maxChecks) {
                    clearInterval(readyInterval);
                    compareBtn.disabled = false;
                    compareBtn.textContent = 'Compare (Basic Mode)';
                    console.warn('Diff library failed to load, using basic comparison');
                }
            }, 100);
            
            // --- Event Listeners ---

            compareBtn.addEventListener('click', runComparison);
            clearBtn.addEventListener('click', clearAll);
            copyBtn.addEventListener('click', copyDiffToClipboard);
            testBtn.addEventListener('click', testLibrary);

            // File upload listeners
            fileInput1.addEventListener('change', (e) => handleFileUpload(e, textInput1));
            fileInput2.addEventListener('change', (e) => handleFileUpload(e, textInput2));
            
            // Scroll synchronization listeners
            let isSyncing = false;
            diffOutput1.addEventListener('scroll', () => {
                if (!isSyncing) {
                    isSyncing = true;
                    diffOutput2.scrollTop = diffOutput1.scrollTop;
                    requestAnimationFrame(() => isSyncing = false);
                }
            });
            diffOutput2.addEventListener('scroll', () => {
                if (!isSyncing) {
                    isSyncing = true;
                    diffOutput1.scrollTop = diffOutput2.scrollTop;
                    requestAnimationFrame(() => isSyncing = false);
                }
            });

            /**
             * Shows a custom notification message.
             * @param {string} message - The message to display.
             * @param {string} type - 'success' or 'error'.
             */
            function showNotification(message, type = 'success') {
                const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
                const notification = document.createElement('div');
                notification.className = `notification p-4 rounded-lg text-white shadow-lg ${bgColor}`;
                notification.textContent = message;
                
                notificationContainer.appendChild(notification);
                
                // Trigger the animation
                setTimeout(() => notification.classList.add('show'), 10);

                // Remove the notification after some time
                setTimeout(() => {
                    notification.classList.remove('show');
                    notification.addEventListener('transitionend', () => notification.remove());
                }, 3000);
            }

            /**
             * Handles reading a file and placing its content into a textarea.
             * @param {Event} event - The file input change event.
             * @param {HTMLTextAreaElement} targetTextarea - The textarea to populate.
             */
            function handleFileUpload(event, targetTextarea) {
                const file = event.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = (e) => {
                    targetTextarea.value = e.target.result;
                };
                reader.onerror = () => {
                    showNotification('Error reading file.', 'error');
                };
                reader.readAsText(file);
            }

            /**
             * Clears all inputs, outputs, and resets the view.
             */
            function clearAll() {
                textInput1.value = '';
                textInput2.value = '';
                diffOutput1.innerHTML = '';
                diffOutput2.innerHTML = '';
                statsDisplay.innerHTML = '';
                resultsContainer.classList.add('hidden');
                initialMessage.classList.remove('hidden');
                fileInput1.value = '';
                fileInput2.value = '';
            }
            
            /**
             * Copies a plain text representation of the diff to the clipboard.
             */
            function copyDiffToClipboard() {
                let diffText = "--- Original\n+++ Changed\n\n";
                const lineElements1 = diffOutput1.children;
                const lineElements2 = diffOutput2.children;
                const maxLength = Math.max(lineElements1.length, lineElements2.length);

                for (let i = 0; i < maxLength; i++) {
                    const lineEl1 = lineElements1[i];
                    const lineEl2 = lineElements2[i];
                    
                    const content1 = lineEl1?.querySelector('.line-content')?.textContent || '';
                    const content2 = lineEl2?.querySelector('.line-content')?.textContent || '';

                    if (lineEl1?.classList.contains('line-removed')) {
                        diffText += `- ${content1}\n`;
                    } else if (lineEl2?.classList.contains('line-added')) {
                        diffText += `+ ${content2}\n`;
                    } else if (lineEl1 && !lineEl1.classList.contains('line-placeholder')) {
                        // This is a common line, copy it, including blank ones.
                        diffText += `  ${content1}\n`;
                    }
                }
                
                navigator.clipboard.writeText(diffText).then(() => {
                    showNotification('Diff copied to clipboard!', 'success');
                }).catch(err => {
                    console.error('Failed to copy diff: ', err);
                    showNotification('Could not copy to clipboard.', 'error');
                });
            }


            /**
             * Test function to verify library functionality.
             */
            function testLibrary() {
                try {
                    console.log('Testing library...');
                    const DiffLib = window.Diff || Diff || JsDiff;

                    if (DiffLib) {
                        console.log('Library found:', DiffLib);
                        const testResult = DiffLib.diffLines('test1\ntest2', 'test1\ntest3');
                        console.log('Test result:', testResult);
                        showNotification('Library test successful!', 'success');
                    } else {
                        console.log('No library found, testing basic diff');
                        const testResult = basicDiffLines('test1\ntest2', 'test1\ntest3');
                        console.log('Basic diff test result:', testResult);
                        showNotification('Basic diff test successful!', 'success');
                    }
                } catch (error) {
                    console.error('Test error:', error);
                    showNotification(`Test failed: ${error.message}`, 'error');
                }
            }

            /**
             * Main function to trigger the diffing process.
             */
            function runComparison() {
                try {
                    compareBtn.textContent = 'Comparing...';
                    compareBtn.disabled = true;

                    setTimeout(() => {
                        try {
                            const originalText = textInput1.value;
                            const changedText = textInput2.value;

                            if (originalText.trim() === '' && changedText.trim() === '') {
                                showNotification('Please enter some text to compare.', 'error');
                                compareBtn.textContent = 'Compare';
                                compareBtn.disabled = false;
                                return;
                            }

                            const options = {
                                ignoreWhitespace: ignoreWhitespaceCheck.checked,
                                ignoreCase: !caseSensitiveCheck.checked,
                                showLineNumbers: showLineNumbersCheck.checked,
                                showBlankLines: showBlankLinesCheck.checked,
                                wordLevelDiff: wordLevelDiffCheck.checked,
                            };

                            console.log('Starting comparison with options:', options);

                            // FIX: Use the correct Diff reference
                            const DiffLib = window.Diff || Diff || JsDiff;
                            let diff;

                            if (!DiffLib) {
                                console.warn('Using basic diff fallback');
                                diff = basicDiffLines(originalText, changedText);
                            } else {
                                console.log('Using jsdiff library');
                                diff = DiffLib.diffLines(originalText, changedText, options);
                            }

                            console.log('Diff result:', diff);
                            renderDiff(diff, options);

                            compareBtn.textContent = 'Compare';
                            compareBtn.disabled = false;
                        } catch (error) {
                            console.error('Error during comparison:', error);
                            console.error('Error stack:', error.stack);
                            console.error('Error name:', error.name);
                            console.error('Error message:', error.message);
                            showNotification(`Error: ${error.message}`, 'error');
                            compareBtn.textContent = 'Compare';
                            compareBtn.disabled = false;
                        }
                    }, 10);
                } catch (error) {
                    console.error('Error in runComparison:', error);
                    showNotification('An error occurred.', 'error');
                    compareBtn.textContent = 'Compare';
                    compareBtn.disabled = false;
                }
            }

            /**
             * Renders the diff results into the output panels.
             * @param {Array} diff - The array of diff parts from jsdiff.
             * @param {Object} options - The comparison options.
             */
            function renderDiff(diff, options) {
                try {
                    console.log('renderDiff called with:', { diff, options });

                    let leftPanelHtml = '';
                    let rightPanelHtml = '';
                    let leftLineNum = 1;
                    let rightLineNum = 1;
                    const stats = { added: 0, deleted: 0, common: 0, modified: 0 };

                diff.forEach((part, index) => {
                    if (part.processed) return; // Skip parts already handled by modification logic

                    const lines = part.value.split('\n');
                    if (lines[lines.length - 1] === '') lines.pop();

                    const isModification = part.removed && diff[index + 1]?.added;
                    if (isModification && options.wordLevelDiff) {
                        const nextPart = diff[index + 1];
                        const oldLines = lines;
                        const newLines = nextPart.value.split('\n');
                        if (newLines[newLines.length - 1] === '') newLines.pop();
                        
                        const maxLen = Math.max(oldLines.length, newLines.length);
                        stats.modified += maxLen;
                        
                        for(let i = 0; i < maxLen; i++) {
                            const oldLine = oldLines[i];
                            const newLine = newLines[i];

                            if (oldLine !== undefined && newLine !== undefined) {
                                // FIX: Use the correct Diff reference
                                const DiffLib = window.Diff || Diff || JsDiff;
                                let wordDiff;

                                if (DiffLib && DiffLib.diffWords) {
                                    wordDiff = DiffLib.diffWords(oldLine, newLine, options);
                                } else {
                                    // Basic word diff fallback
                                    wordDiff = basicDiffWords(oldLine, newLine);
                                }
                                let leftLineContent = '';
                                let rightLineContent = '';
                                wordDiff.forEach(wordPart => {
                                    const escapedContent = escapeHtml(wordPart.value);
                                    if (wordPart.added) {
                                        rightLineContent += `<span class="line-modified-word">${escapedContent}</span>`;
                                    } else if (wordPart.removed) {
                                        leftLineContent += `<span class="line-modified-word">${escapedContent}</span>`;
                                    } else {
                                        leftLineContent += escapedContent;
                                        rightLineContent += escapedContent;
                                    }
                                });
                                leftPanelHtml += createLineHtml(leftLineNum++, leftLineContent, 'line-removed', options);
                                rightPanelHtml += createLineHtml(rightLineNum++, rightLineContent, 'line-added', options);
                            } else if (oldLine !== undefined) {
                                leftPanelHtml += createLineHtml(leftLineNum++, escapeHtml(oldLine), 'line-removed', options);
                                rightPanelHtml += createLineHtml('&nbsp;', '', 'line-placeholder', options);
                            } else if (newLine !== undefined) {
                                leftPanelHtml += createLineHtml('&nbsp;', '', 'line-placeholder', options);
                                rightPanelHtml += createLineHtml(rightLineNum++, escapeHtml(newLine), 'line-added', options);
                            }
                        }
                        nextPart.processed = true;
                    } else {
                        lines.forEach(line => {
                            // Always show lines, even blank ones, but apply the showBlankLines option for styling
                            const escapedLine = escapeHtml(line);
                            if (part.added) {
                                stats.added++;
                                rightPanelHtml += createLineHtml(rightLineNum++, escapedLine, 'line-added', options);
                                leftPanelHtml += createLineHtml('&nbsp;', '', 'line-placeholder', options);
                            } else if (part.removed) {
                                stats.deleted++;
                                leftPanelHtml += createLineHtml(leftLineNum++, escapedLine, 'line-removed', options);
                                rightPanelHtml += createLineHtml('&nbsp;', '', 'line-placeholder', options);
                            } else {
                                stats.common++;
                                leftPanelHtml += createLineHtml(leftLineNum++, escapedLine, '', options);
                                rightPanelHtml += createLineHtml(rightLineNum++, escapedLine, '', options);
                            }
                        });
                    }
                });

                diffOutput1.innerHTML = leftPanelHtml;
                diffOutput2.innerHTML = rightPanelHtml;
                updateStats(stats);

                resultsContainer.classList.remove('hidden');
                initialMessage.classList.add('hidden');
                } catch (error) {
                    console.error('Error in renderDiff:', error);
                    showNotification('Error rendering diff results.', 'error');
                }
            }

            /**
             * Creates the HTML for a single line in the diff output.
             */
            function createLineHtml(num, content, typeClass, options) {
                try {
                    let lineClass = 'line';
                    if (typeClass) lineClass += ` ${typeClass}`;

                    // Safe content handling
                    const safeContent = content || '';
                    if (safeContent.trim() === '') lineClass += ' line-blank';

                    const numHtml = options.showLineNumbers ? `<div class="line-num">${num}</div>` : '';
                    return `<div class="${lineClass}">${numHtml}<div class="line-content">${safeContent || '&nbsp;'}</div></div>`;
                } catch (error) {
                    console.error('Error in createLineHtml:', error);
                    return `<div class="line"><div class="line-content">&nbsp;</div></div>`;
                }
            }
            
            /**
             * Updates the statistics display area.
             */
            function updateStats(stats) {
                const modifiedText = stats.modified > 0 ? ` (Modified: ${stats.modified})` : '';
                statsDisplay.innerHTML = `
                    <span class="text-slate-500">Stats:</span>
                    <span class="text-green-600">Added: ${stats.added}</span>
                    <span class="text-red-600">Deleted: ${stats.deleted}</span>
                    <span class="text-slate-700">Common: ${stats.common}</span>
                    <span class="text-blue-600">${modifiedText}</span>
                `;
            }

            /**
             * Basic word diff fallback function.
             */
            function basicDiffWords(oldLine, newLine) {
                try {
                    const safeOldLine = oldLine || '';
                    const safeNewLine = newLine || '';

                    const oldWords = safeOldLine.split(/(\s+)/);
                    const newWords = safeNewLine.split(/(\s+)/);
                    const diff = [];

                    // Simple word comparison
                    const maxLength = Math.max(oldWords.length, newWords.length);
                    for (let i = 0; i < maxLength; i++) {
                        const oldWord = oldWords[i];
                        const newWord = newWords[i];

                        if (oldWord === newWord) {
                            if (oldWord !== undefined) {
                                diff.push({ value: oldWord });
                            }
                        } else {
                            if (oldWord !== undefined) {
                                diff.push({ removed: true, value: oldWord });
                            }
                            if (newWord !== undefined) {
                                diff.push({ added: true, value: newWord });
                            }
                        }
                    }

                    return diff;
                } catch (error) {
                    console.error('Error in basicDiffWords:', error);
                    return [{ value: oldLine || '' }, { value: newLine || '' }];
                }
            }

            /**
             * Basic diff fallback function when jsdiff library is not available.
             */
            function basicDiffLines(oldText, newText) {
                try {
                    const safeOldText = oldText || '';
                    const safeNewText = newText || '';

                    const oldLines = safeOldText.split('\n');
                    const newLines = safeNewText.split('\n');
                    const diff = [];

                    // Simple line-by-line comparison
                    const maxLength = Math.max(oldLines.length, newLines.length);
                    let oldIndex = 0;
                    let newIndex = 0;

                    while (oldIndex < oldLines.length || newIndex < newLines.length) {
                        const oldLine = oldLines[oldIndex];
                        const newLine = newLines[newIndex];

                        if (oldIndex >= oldLines.length) {
                            // Only new lines left
                            diff.push({ added: true, value: (newLine || '') + '\n' });
                            newIndex++;
                        } else if (newIndex >= newLines.length) {
                            // Only old lines left
                            diff.push({ removed: true, value: (oldLine || '') + '\n' });
                            oldIndex++;
                        } else if (oldLine === newLine) {
                            // Lines are the same
                            diff.push({ value: (oldLine || '') + '\n' });
                            oldIndex++;
                            newIndex++;
                        } else {
                            // Lines are different - mark as removed and added
                            diff.push({ removed: true, value: (oldLine || '') + '\n' });
                            diff.push({ added: true, value: (newLine || '') + '\n' });
                            oldIndex++;
                            newIndex++;
                        }
                    }

                    return diff;
                } catch (error) {
                    console.error('Error in basicDiffLines:', error);
                    return [
                        { removed: true, value: oldText || '' },
                        { added: true, value: newText || '' }
                    ];
                }
            }

            /**
             * Escapes HTML special characters to prevent XSS and rendering issues.
             */
            function escapeHtml(str) {
                if (str === null || str === undefined) {
                    return '';
                }
                return String(str)
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");
            }
        });
    </script>
</body>
</html>
