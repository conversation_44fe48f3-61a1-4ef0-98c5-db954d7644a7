<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intelligent Diff Checker Tool</title>
    
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- jsdiff library for the core diffing logic -->
    <script src="https://unpkg.com/diff@5.1.0/dist/diff.min.js" onload="console.log('jsdiff library loaded')" onerror="console.error('Failed to load jsdiff library from unpkg')"></script>
    <!-- Fallback CDN -->
    <script>
        // Fallback if unpkg fails
        if (typeof window.Diff === 'undefined') {
            console.log('Loading fallback diff library...');
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/diff@5.1.0/dist/diff.min.js';
            script.onload = () => console.log('Fallback diff library loaded');
            script.onerror = () => console.error('Fallback diff library failed to load');
            document.head.appendChild(script);
        }
    </script>
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet">

    <style>
        /* Custom styles for the application */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc; /* slate-50 */
        }
        .diff-font {
            font-family: 'Fira Code', monospace;
        }
        /* Custom scrollbar for a cleaner look */
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9; /* slate-100 */
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1; /* slate-300 */
            border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8; /* slate-400 */
        }

        /* Performance optimizations for large files */
        .virtual-scroll-container {
            position: relative;
            overflow: auto;
            height: 100%;
        }

        .virtual-scroll-content {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
        }

        /* Enhanced line styles */
        .line {
            display: flex;
            min-height: 1.5rem;
            line-height: 1.5rem;
            padding: 0 0.5rem;
            transition: background-color 0.1s ease;
        }

        .line:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        /* Loading spinner */
        .spinner {
            border: 2px solid #f3f4f6;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Progress bar */
        .progress-bar {
            width: 100%;
            height: 4px;
            background-color: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: #3b82f6;
            transition: width 0.3s ease;
        }
        .line-num {
            width: 50px;
            min-width: 50px;
            text-align: right;
            padding-right: 12px;
            color: #64748b; /* slate-500 */
            border-right: 1px solid #e2e8f0; /* slate-200 */
            background-color: #f8fafc; /* slate-50 */
            -webkit-user-select: none; /* Safari */
            -ms-user-select: none; /* IE 10+ */
            user-select: none;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .line-content {
            padding-left: 12px;
            white-space: pre-wrap;
            word-break: break-word;
            flex-grow: 1;
            font-family: 'Fira Code', monospace;
        }
        .line-added {
            background-color: #dcfce7; /* Enhanced green */
            border-left: 3px solid #22c55e;
        }
        .line-removed {
            background-color: #fef2f2; /* Enhanced red */
            border-left: 3px solid #ef4444;
        }
        .line-modified {
            background-color: #fef3c7; /* Enhanced yellow */
            border-left: 3px solid #f59e0b;
        }
        .line-modified-word {
            background-color: #dbeafe; /* blue-100 */
            border-radius: 3px;
            padding: 1px 2px;
            margin: 0 1px;
            border: 1px solid #93c5fd; /* blue-300 */
        }
        .line-blank {
            background-color: #f1f5f9; /* slate-100 */
            border-left: 3px solid #cbd5e1;
        }
        .line-placeholder {
            background-color: #f9fafb; /* gray-50 */
            color: #9ca3af; /* gray-400 */
        }
        /* Disable resize handle on textareas */
        textarea {
            resize: none;
        }
        /* Custom notification styles */
        .notification {
            transition: all 0.5s ease-in-out;
            opacity: 0;
            transform: translateX(100%);
            max-width: 350px;
        }
        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }
    </style>
</head>
<body class="text-slate-800">

    <div class="container mx-auto p-4 md:p-6 lg:p-8">
        <!-- Header Section -->
        <header class="mb-6 text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-slate-900">Intelligent Diff Checker</h1>
            <p class="mt-2 text-slate-600">Compare text, files, and code with advanced highlighting.</p>
        </header>

        <!-- Controls and Options Section -->
        <div class="bg-white p-4 rounded-xl shadow-md mb-6">
            <!-- Progress Bar (hidden by default) -->
            <div id="progress-container" class="hidden mb-4">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm text-gray-600">Processing...</span>
                    <span id="progress-text" class="text-sm text-gray-600">0%</span>
                </div>
                <div class="progress-bar">
                    <div id="progress-fill" class="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 items-center">
                <!-- Main Action Button -->
                <div class="col-span-2 sm:col-span-3 md:col-span-1">
                    <button id="compare-btn" class="w-full bg-blue-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 disabled:bg-slate-400 disabled:cursor-not-allowed flex items-center justify-center">
                        <span id="compare-text">Compare</span>
                        <div id="compare-spinner" class="spinner ml-2 hidden"></div>
                    </button>
                </div>
                <!-- Toggle Options -->
                <div class="col-span-2 sm:col-span-3 md:col-span-3 lg:col-span-5 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-x-4 gap-y-2">
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" id="show-line-numbers" class="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" checked>
                        <span class="text-sm text-slate-700">Line Numbers</span>
                    </label>
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" id="show-blank-lines" class="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" checked>
                        <span class="text-sm text-slate-700">Show Blank Lines</span>
                    </label>
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" id="ignore-whitespace" class="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                        <span class="text-sm text-slate-700">Ignore Whitespace</span>
                    </label>
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" id="case-sensitive" class="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" checked>
                        <span class="text-sm text-slate-700">Case Sensitive</span>
                    </label>
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" id="word-level-diff" class="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" checked>
                        <span class="text-sm text-slate-700">Word-Level Diff</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Input Panels Section -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Left Panel -->
            <div class="bg-white rounded-xl shadow-md p-1">
                <div class="flex justify-between items-center p-3 border-b border-slate-200">
                    <div>
                        <h2 class="font-semibold text-lg">Original Text</h2>
                        <div id="file-info-1" class="text-xs text-gray-500 mt-1 hidden">
                            <span id="file-name-1"></span> • <span id="file-size-1"></span> • <span id="line-count-1"></span>
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <input type="file" id="file-input-1" class="hidden" accept=".txt,.js,.html,.css,.md,.json,.py,.java,.cpp,.c,.xml,.yaml,.yml,.sql,.sh,.bat,.ps1">
                        <button onclick="document.getElementById('file-input-1').click()" class="text-sm bg-slate-100 hover:bg-slate-200 text-slate-700 font-medium py-1 px-3 rounded-md transition-colors">Upload File</button>
                        <button id="clear-1" class="text-sm bg-red-100 hover:bg-red-200 text-red-700 font-medium py-1 px-3 rounded-md transition-colors">Clear</button>
                    </div>
                </div>
                <textarea id="text-input-1" class="w-full h-64 p-3 diff-font text-sm border-0 focus:ring-0 custom-scrollbar" placeholder="Paste original text here or upload a file...">Hello World
This is line 2
This is line 3
Common line</textarea>
            </div>
            <!-- Right Panel -->
            <div class="bg-white rounded-xl shadow-md p-1">
                 <div class="flex justify-between items-center p-3 border-b border-slate-200">
                    <div>
                        <h2 class="font-semibold text-lg">Changed Text</h2>
                        <div id="file-info-2" class="text-xs text-gray-500 mt-1 hidden">
                            <span id="file-name-2"></span> • <span id="file-size-2"></span> • <span id="line-count-2"></span>
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <input type="file" id="file-input-2" class="hidden" accept=".txt,.js,.html,.css,.md,.json,.py,.java,.cpp,.c,.xml,.yaml,.yml,.sql,.sh,.bat,.ps1">
                        <button onclick="document.getElementById('file-input-2').click()" class="text-sm bg-slate-100 hover:bg-slate-200 text-slate-700 font-medium py-1 px-3 rounded-md transition-colors">Upload File</button>
                        <button id="clear-2" class="text-sm bg-red-100 hover:bg-red-200 text-red-700 font-medium py-1 px-3 rounded-md transition-colors">Clear</button>
                    </div>
                </div>
                <textarea id="text-input-2" class="w-full h-64 p-3 diff-font text-sm border-0 focus:ring-0 custom-scrollbar" placeholder="Paste changed text here or upload a file...">Hello Universe
This is line 2 modified
This is a new line 3
Common line
New line added</textarea>
            </div>
        </div>

        <!-- Diff Results Section -->
        <div id="results-container" class="hidden">
            <!-- Stats and Legend -->
            <div class="bg-white p-4 rounded-xl shadow-md mb-6">
                <div class="flex flex-wrap justify-between items-start gap-4">
                    <div>
                        <h3 class="text-sm font-semibold text-gray-700 mb-2">Comparison Statistics</h3>
                        <div id="stats-display" class="grid grid-cols-2 md:grid-cols-4 gap-x-6 gap-y-2 text-sm">
                            <!-- Stats will be injected here -->
                        </div>
                    </div>
                    <div>
                        <h3 class="text-sm font-semibold text-gray-700 mb-2">Legend</h3>
                        <div class="grid grid-cols-2 gap-x-4 gap-y-1 text-xs">
                            <div class="flex items-center">
                                <span class="w-3 h-3 rounded-sm bg-green-200 border-l-2 border-green-500 mr-2"></span>
                                <span>Added</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 rounded-sm bg-red-200 border-l-2 border-red-500 mr-2"></span>
                                <span>Deleted</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 rounded-sm bg-yellow-200 border-l-2 border-yellow-500 mr-2"></span>
                                <span>Modified</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 rounded-sm bg-blue-100 border border-blue-300 mr-2"></span>
                                <span>Word Change</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Info -->
                <div id="performance-info" class="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-500 hidden">
                    <span id="processing-time"></span> • <span id="memory-usage"></span>
                </div>
            </div>

            <!-- Output Panels -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 diff-font text-sm">
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="p-3 font-semibold border-b border-slate-200 bg-slate-50">Original</div>
                    <div id="diff-output-1" class="overflow-auto custom-scrollbar" style="max-height: 60vh;"></div>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="p-3 font-semibold border-b border-slate-200 bg-slate-50">Changed</div>
                    <div id="diff-output-2" class="overflow-auto custom-scrollbar" style="max-height: 60vh;"></div>
                </div>
            </div>
            
            <!-- Action Buttons for Results -->
            <div class="mt-6 flex justify-center gap-4">
                <button id="copy-btn" class="bg-slate-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-slate-700 transition-colors">Copy Diff</button>
                <button id="clear-btn" class="bg-red-500 text-white font-bold py-2 px-6 rounded-lg hover:bg-red-600 transition-colors">Clear All</button>
                <button id="test-btn" class="bg-green-500 text-white font-bold py-2 px-6 rounded-lg hover:bg-green-600 transition-colors">Test Library</button>
            </div>
        </div>
        
        <!-- Initial state message -->
        <div id="initial-message" class="text-center py-10 px-6 bg-white rounded-xl shadow-md">
            <h3 class="text-xl font-semibold text-slate-700">Ready to Compare</h3>
            <p class="text-slate-500 mt-2">Paste your text into the panels above, upload files, or drag & drop files to compare.</p>

            <!-- Keyboard shortcuts help -->
            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-sm font-semibold text-gray-700 mb-2">Keyboard Shortcuts</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-600">
                    <div><kbd class="bg-white px-2 py-1 rounded border">Ctrl+Enter</kbd> Compare</div>
                    <div><kbd class="bg-white px-2 py-1 rounded border">Ctrl+R</kbd> Clear All</div>
                    <div><kbd class="bg-white px-2 py-1 rounded border">Ctrl+C</kbd> Copy Diff</div>
                    <div><kbd class="bg-white px-2 py-1 rounded border">Esc</kbd> Clear All</div>
                </div>
            </div>

            <!-- Features list -->
            <div class="mt-4 text-xs text-gray-500">
                <p>✨ Features: Large file support • Word-level diff • Drag & drop • Real-time stats • Export options</p>
            </div>
        </div>
    </div>

    <!-- Notification container for custom alerts -->
    <div id="notification-container" class="fixed top-5 right-5 z-50 flex flex-col items-end gap-2"></div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM Content Loaded - Initializing Diff Checker');
            // DOM element references
            const textInput1 = document.getElementById('text-input-1');
            const textInput2 = document.getElementById('text-input-2');
            const fileInput1 = document.getElementById('file-input-1');
            const fileInput2 = document.getElementById('file-input-2');
            const compareBtn = document.getElementById('compare-btn');
            const clearBtn = document.getElementById('clear-btn');
            const copyBtn = document.getElementById('copy-btn');
            const testBtn = document.getElementById('test-btn');
            const clear1Btn = document.getElementById('clear-1');
            const clear2Btn = document.getElementById('clear-2');

            // Progress and performance elements
            const progressContainer = document.getElementById('progress-container');
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');
            const compareText = document.getElementById('compare-text');
            const compareSpinner = document.getElementById('compare-spinner');
            const performanceInfo = document.getElementById('performance-info');
            const processingTime = document.getElementById('processing-time');
            const memoryUsage = document.getElementById('memory-usage');

            // File info elements
            const fileInfo1 = document.getElementById('file-info-1');
            const fileName1 = document.getElementById('file-name-1');
            const fileSize1 = document.getElementById('file-size-1');
            const lineCount1 = document.getElementById('line-count-1');
            const fileInfo2 = document.getElementById('file-info-2');
            const fileName2 = document.getElementById('file-name-2');
            const fileSize2 = document.getElementById('file-size-2');
            const lineCount2 = document.getElementById('line-count-2');
            const diffOutput1 = document.getElementById('diff-output-1');
            const diffOutput2 = document.getElementById('diff-output-2');
            const resultsContainer = document.getElementById('results-container');
            const initialMessage = document.getElementById('initial-message');
            const statsDisplay = document.getElementById('stats-display');
            const notificationContainer = document.getElementById('notification-container');

            // Toggle options
            const showLineNumbersCheck = document.getElementById('show-line-numbers');
            const showBlankLinesCheck = document.getElementById('show-blank-lines');
            const ignoreWhitespaceCheck = document.getElementById('ignore-whitespace');
            const caseSensitiveCheck = document.getElementById('case-sensitive');
            const wordLevelDiffCheck = document.getElementById('word-level-diff');

            // --- FIX: Wait for jsdiff library to load before enabling the compare button ---
            compareBtn.disabled = true;
            compareBtn.textContent = 'Loading Tool...';

            let checkCount = 0;
            const maxChecks = 50; // 5 seconds timeout
            const readyInterval = setInterval(() => {
                checkCount++;
                // Check for both possible ways the library might be exposed
                if (typeof window.Diff !== 'undefined' || typeof Diff !== 'undefined' || typeof JsDiff !== 'undefined') {
                    clearInterval(readyInterval);
                    compareBtn.disabled = false;
                    compareBtn.textContent = 'Compare';
                    console.log('Diff library loaded successfully');

                    // Test the library
                    try {
                        const DiffLib = window.Diff || Diff || JsDiff;
                        const testDiff = DiffLib.diffLines('test1', 'test2');
                        console.log('Diff library test successful:', testDiff);
                    } catch (e) {
                        console.warn('Diff library test failed:', e);
                    }
                } else if (checkCount >= maxChecks) {
                    clearInterval(readyInterval);
                    compareBtn.disabled = false;
                    compareBtn.textContent = 'Compare (Basic Mode)';
                    console.warn('Diff library failed to load, using basic comparison');
                }
            }, 100);
            
            // --- Event Listeners ---

            compareBtn.addEventListener('click', runComparison);
            clearBtn.addEventListener('click', clearAll);
            copyBtn.addEventListener('click', copyDiffToClipboard);
            testBtn.addEventListener('click', testLibrary);
            clear1Btn.addEventListener('click', () => clearPanel(1));
            clear2Btn.addEventListener('click', () => clearPanel(2));

            // File upload listeners
            fileInput1.addEventListener('change', (e) => handleFileUpload(e, textInput1, 1));
            fileInput2.addEventListener('change', (e) => handleFileUpload(e, textInput2, 2));

            // Text input listeners for real-time stats
            textInput1.addEventListener('input', () => updateTextStats(1));
            textInput2.addEventListener('input', () => updateTextStats(2));

            // Keyboard shortcuts
            document.addEventListener('keydown', handleKeyboardShortcuts);

            // Drag and drop support
            setupDragAndDrop();
            
            // Scroll synchronization listeners
            let isSyncing = false;
            diffOutput1.addEventListener('scroll', () => {
                if (!isSyncing) {
                    isSyncing = true;
                    diffOutput2.scrollTop = diffOutput1.scrollTop;
                    requestAnimationFrame(() => isSyncing = false);
                }
            });
            diffOutput2.addEventListener('scroll', () => {
                if (!isSyncing) {
                    isSyncing = true;
                    diffOutput1.scrollTop = diffOutput2.scrollTop;
                    requestAnimationFrame(() => isSyncing = false);
                }
            });

            /**
             * Shows a custom notification message.
             * @param {string} message - The message to display.
             * @param {string} type - 'success' or 'error'.
             */
            function showNotification(message, type = 'success') {
                const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
                const notification = document.createElement('div');
                notification.className = `notification p-4 rounded-lg text-white shadow-lg ${bgColor}`;
                notification.textContent = message;
                
                notificationContainer.appendChild(notification);
                
                // Trigger the animation
                setTimeout(() => notification.classList.add('show'), 10);

                // Remove the notification after some time
                setTimeout(() => {
                    notification.classList.remove('show');
                    notification.addEventListener('transitionend', () => notification.remove());
                }, 3000);
            }

            /**
             * Handles reading a file and placing its content into a textarea.
             * @param {Event} event - The file input change event.
             * @param {HTMLTextAreaElement} targetTextarea - The textarea to populate.
             * @param {number} panelNumber - Panel number (1 or 2) for file info display.
             */
            function handleFileUpload(event, targetTextarea, panelNumber) {
                const file = event.target.files[0];
                if (!file) return;

                // Check file size (warn if > 5MB)
                const maxSize = 5 * 1024 * 1024; // 5MB
                if (file.size > maxSize) {
                    const sizeMB = (file.size / (1024 * 1024)).toFixed(2);
                    if (!confirm(`This file is ${sizeMB}MB. Large files may take time to process. Continue?`)) {
                        event.target.value = ''; // Clear the input
                        return;
                    }
                }

                const reader = new FileReader();

                reader.onloadstart = () => {
                    showNotification(`Loading ${file.name}...`, 'info');
                };

                reader.onload = (e) => {
                    const content = e.target.result;
                    targetTextarea.value = content;

                    // Update file info
                    updateFileInfo(file, content, panelNumber);
                    updateTextStats(panelNumber);

                    showNotification(`File loaded: ${file.name}`, 'success');
                };

                reader.onerror = () => {
                    showNotification('Error reading file.', 'error');
                };

                reader.readAsText(file);
            }

            /**
             * Updates file information display.
             */
            function updateFileInfo(file, content, panelNumber) {
                const fileInfo = panelNumber === 1 ? fileInfo1 : fileInfo2;
                const fileName = panelNumber === 1 ? fileName1 : fileName2;
                const fileSize = panelNumber === 1 ? fileSize1 : fileSize2;
                const lineCount = panelNumber === 1 ? lineCount1 : lineCount2;

                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                lineCount.textContent = `${content.split('\n').length} lines`;

                fileInfo.classList.remove('hidden');
            }

            /**
             * Updates text statistics for a panel.
             */
            function updateTextStats(panelNumber) {
                const textarea = panelNumber === 1 ? textInput1 : textInput2;
                const lineCount = panelNumber === 1 ? lineCount1 : lineCount2;

                if (textarea.value) {
                    const lines = textarea.value.split('\n').length;
                    lineCount.textContent = `${lines} lines`;
                }
            }

            /**
             * Formats file size in human readable format.
             */
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            /**
             * Clears a specific panel.
             */
            function clearPanel(panelNumber) {
                const textarea = panelNumber === 1 ? textInput1 : textInput2;
                const fileInput = panelNumber === 1 ? fileInput1 : fileInput2;
                const fileInfo = panelNumber === 1 ? fileInfo1 : fileInfo2;

                textarea.value = '';
                fileInput.value = '';
                fileInfo.classList.add('hidden');
            }

            /**
             * Clears all inputs, outputs, and resets the view.
             */
            function clearAll() {
                textInput1.value = '';
                textInput2.value = '';
                diffOutput1.innerHTML = '';
                diffOutput2.innerHTML = '';
                statsDisplay.innerHTML = '';
                resultsContainer.classList.add('hidden');
                initialMessage.classList.remove('hidden');
                fileInput1.value = '';
                fileInput2.value = '';
            }
            
            /**
             * Copies a plain text representation of the diff to the clipboard.
             */
            function copyDiffToClipboard() {
                let diffText = "--- Original\n+++ Changed\n\n";
                const lineElements1 = diffOutput1.children;
                const lineElements2 = diffOutput2.children;
                const maxLength = Math.max(lineElements1.length, lineElements2.length);

                for (let i = 0; i < maxLength; i++) {
                    const lineEl1 = lineElements1[i];
                    const lineEl2 = lineElements2[i];
                    
                    const content1 = lineEl1?.querySelector('.line-content')?.textContent || '';
                    const content2 = lineEl2?.querySelector('.line-content')?.textContent || '';

                    if (lineEl1?.classList.contains('line-removed')) {
                        diffText += `- ${content1}\n`;
                    } else if (lineEl2?.classList.contains('line-added')) {
                        diffText += `+ ${content2}\n`;
                    } else if (lineEl1 && !lineEl1.classList.contains('line-placeholder')) {
                        // This is a common line, copy it, including blank ones.
                        diffText += `  ${content1}\n`;
                    }
                }
                
                navigator.clipboard.writeText(diffText).then(() => {
                    showNotification('Diff copied to clipboard!', 'success');
                }).catch(err => {
                    console.error('Failed to copy diff: ', err);
                    showNotification('Could not copy to clipboard.', 'error');
                });
            }


            /**
             * Test function to verify library functionality.
             */
            function testLibrary() {
                try {
                    console.log('Testing library...');
                    const DiffLib = window.Diff || Diff || JsDiff;

                    if (DiffLib) {
                        console.log('Library found:', DiffLib);
                        const testResult = DiffLib.diffLines('test1\ntest2', 'test1\ntest3');
                        console.log('Test result:', testResult);
                        showNotification('Library test successful!', 'success');
                    } else {
                        console.log('No library found, testing basic diff');
                        const testResult = basicDiffLines('test1\ntest2', 'test1\ntest3');
                        console.log('Basic diff test result:', testResult);
                        showNotification('Basic diff test successful!', 'success');
                    }
                } catch (error) {
                    console.error('Test error:', error);
                    showNotification(`Test failed: ${error.message}`, 'error');
                }
            }

            /**
             * Main function to trigger the diffing process.
             */
            async function runComparison() {
                try {
                    const startTime = performance.now();

                    // Show loading state
                    showLoadingState(true);

                    const originalText = textInput1.value;
                    const changedText = textInput2.value;

                    if (originalText.trim() === '' && changedText.trim() === '') {
                        showNotification('Please enter some text to compare.', 'error');
                        showLoadingState(false);
                        return;
                    }

                    // Check for large files
                    const totalSize = originalText.length + changedText.length;
                    const isLargeFile = totalSize > 100000; // 100KB threshold

                    if (isLargeFile) {
                        showNotification('Processing large files...', 'info');
                    }

                    const options = {
                        ignoreWhitespace: ignoreWhitespaceCheck.checked,
                        ignoreCase: !caseSensitiveCheck.checked,
                        showLineNumbers: showLineNumbersCheck.checked,
                        showBlankLines: showBlankLinesCheck.checked,
                        wordLevelDiff: wordLevelDiffCheck.checked,
                    };

                    console.log('Starting comparison with options:', options);
                    updateProgress(10, 'Initializing comparison...');

                    // Use appropriate diff method
                    const DiffLib = window.Diff || Diff || JsDiff;
                    let diff;

                    if (!DiffLib) {
                        console.warn('Using basic diff fallback');
                        updateProgress(30, 'Using basic diff algorithm...');
                        diff = await processWithProgress(basicDiffLines, originalText, changedText);
                    } else {
                        console.log('Using jsdiff library');
                        updateProgress(30, 'Using advanced diff algorithm...');
                        diff = await processWithProgress(() => DiffLib.diffLines(originalText, changedText, options));
                    }

                    updateProgress(70, 'Rendering results...');
                    console.log('Diff result:', diff);

                    // Render with chunking for large results
                    await renderDiffWithProgress(diff, options);

                    updateProgress(100, 'Complete!');

                    // Show performance info
                    const endTime = performance.now();
                    const processingTimeMs = Math.round(endTime - startTime);
                    showPerformanceInfo(processingTimeMs, totalSize);

                    setTimeout(() => {
                        showLoadingState(false);
                        showNotification('Comparison completed successfully!', 'success');
                    }, 500);

                } catch (error) {
                    console.error('Error during comparison:', error);
                    showNotification(`Error: ${error.message}`, 'error');
                    showLoadingState(false);
                }
            }

            /**
             * Shows/hides loading state with progress.
             */
            function showLoadingState(show) {
                if (show) {
                    compareText.textContent = 'Processing...';
                    compareSpinner.classList.remove('hidden');
                    compareBtn.disabled = true;
                    progressContainer.classList.remove('hidden');
                } else {
                    compareText.textContent = 'Compare';
                    compareSpinner.classList.add('hidden');
                    compareBtn.disabled = false;
                    progressContainer.classList.add('hidden');
                    updateProgress(0, '');
                }
            }

            /**
             * Updates progress bar and text.
             */
            function updateProgress(percentage, text) {
                progressFill.style.width = `${percentage}%`;
                progressText.textContent = text || `${percentage}%`;
            }

            /**
             * Processes a function with progress indication.
             */
            async function processWithProgress(fn, ...args) {
                return new Promise((resolve) => {
                    setTimeout(() => {
                        const result = fn(...args);
                        resolve(result);
                    }, 10);
                });
            }

            /**
             * Shows performance information.
             */
            function showPerformanceInfo(timeMs, dataSize) {
                processingTime.textContent = `Processed in ${timeMs}ms`;
                memoryUsage.textContent = `Data size: ${formatFileSize(dataSize)}`;
                performanceInfo.classList.remove('hidden');
            }

            /**
             * Renders the diff results into the output panels.
             * @param {Array} diff - The array of diff parts from jsdiff.
             * @param {Object} options - The comparison options.
             */
            /**
             * Enhanced renderDiff with chunked processing for large files.
             */
            async function renderDiffWithProgress(diff, options) {
                try {
                    console.log('renderDiffWithProgress called with:', { diff, options });

                    const stats = { added: 0, deleted: 0, common: 0, modified: 0 };
                    const CHUNK_SIZE = 100; // Process 100 lines at a time

                    // Clear previous results
                    diffOutput1.innerHTML = '';
                    diffOutput2.innerHTML = '';

                    let leftLineNum = 1;
                    let rightLineNum = 1;
                    let processedParts = 0;

                    // Process diff in chunks
                    for (let i = 0; i < diff.length; i += CHUNK_SIZE) {
                        const chunk = diff.slice(i, i + CHUNK_SIZE);
                        const { leftHtml, rightHtml, leftNum, rightNum, chunkStats } =
                            await processChunk(chunk, leftLineNum, rightLineNum, options, i);

                        // Append to DOM
                        diffOutput1.innerHTML += leftHtml;
                        diffOutput2.innerHTML += rightHtml;

                        // Update counters
                        leftLineNum = leftNum;
                        rightLineNum = rightNum;

                        // Update stats
                        stats.added += chunkStats.added;
                        stats.deleted += chunkStats.deleted;
                        stats.common += chunkStats.common;
                        stats.modified += chunkStats.modified;

                        // Update progress
                        const progress = 70 + (30 * (i + CHUNK_SIZE) / diff.length);
                        updateProgress(Math.min(progress, 100), `Rendering... ${Math.round(progress)}%`);

                        // Allow UI to update
                        await new Promise(resolve => setTimeout(resolve, 1));
                    }

                    updateStats(stats);
                    resultsContainer.classList.remove('hidden');
                    initialMessage.classList.add('hidden');

                } catch (error) {
                    console.error('Error in renderDiffWithProgress:', error);
                    showNotification('Error rendering diff results.', 'error');
                }
            }

            /**
             * Process a chunk of diff data.
             */
            async function processChunk(chunk, startLeftNum, startRightNum, options, chunkIndex) {
                let leftPanelHtml = '';
                let rightPanelHtml = '';
                let leftLineNum = startLeftNum;
                let rightLineNum = startRightNum;
                const stats = { added: 0, deleted: 0, common: 0, modified: 0 };

                chunk.forEach((part, index) => {
                    if (part.processed) return; // Skip parts already handled by modification logic

                    const lines = part.value.split('\n');
                    if (lines[lines.length - 1] === '') lines.pop();

                    const isModification = part.removed && chunk[index + 1]?.added;
                    if (isModification && options.wordLevelDiff) {
                        const nextPart = chunk[index + 1];
                        const oldLines = lines;
                        const newLines = nextPart.value.split('\n');
                        if (newLines[newLines.length - 1] === '') newLines.pop();

                        const maxLen = Math.max(oldLines.length, newLines.length);
                        stats.modified += maxLen;

                        for(let i = 0; i < maxLen; i++) {
                            const oldLine = oldLines[i];
                            const newLine = newLines[i];

                            if (oldLine !== undefined && newLine !== undefined) {
                                // Use the correct Diff reference
                                const DiffLib = window.Diff || Diff || JsDiff;
                                let wordDiff;

                                if (DiffLib && DiffLib.diffWords) {
                                    wordDiff = DiffLib.diffWords(oldLine, newLine, options);
                                } else {
                                    // Basic word diff fallback
                                    wordDiff = basicDiffWords(oldLine, newLine);
                                }
                                let leftLineContent = '';
                                let rightLineContent = '';
                                wordDiff.forEach(wordPart => {
                                    const escapedContent = escapeHtml(wordPart.value);
                                    if (wordPart.added) {
                                        rightLineContent += `<span class="line-modified-word">${escapedContent}</span>`;
                                    } else if (wordPart.removed) {
                                        leftLineContent += `<span class="line-modified-word">${escapedContent}</span>`;
                                    } else {
                                        leftLineContent += escapedContent;
                                        rightLineContent += escapedContent;
                                    }
                                });
                                leftPanelHtml += createLineHtml(leftLineNum++, leftLineContent, 'line-modified', options);
                                rightPanelHtml += createLineHtml(rightLineNum++, rightLineContent, 'line-modified', options);
                            } else if (oldLine !== undefined) {
                                leftPanelHtml += createLineHtml(leftLineNum++, escapeHtml(oldLine), 'line-removed', options);
                                rightPanelHtml += createLineHtml('&nbsp;', '', 'line-placeholder', options);
                            } else if (newLine !== undefined) {
                                leftPanelHtml += createLineHtml('&nbsp;', '', 'line-placeholder', options);
                                rightPanelHtml += createLineHtml(rightLineNum++, escapeHtml(newLine), 'line-added', options);
                            }
                        }
                        nextPart.processed = true;
                    } else {
                        lines.forEach(line => {
                            // Always show lines, even blank ones, but apply the showBlankLines option for styling
                            const escapedLine = escapeHtml(line);
                            if (part.added) {
                                stats.added++;
                                rightPanelHtml += createLineHtml(rightLineNum++, escapedLine, 'line-added', options);
                                leftPanelHtml += createLineHtml('&nbsp;', '', 'line-placeholder', options);
                            } else if (part.removed) {
                                stats.deleted++;
                                leftPanelHtml += createLineHtml(leftLineNum++, escapedLine, 'line-removed', options);
                                rightPanelHtml += createLineHtml('&nbsp;', '', 'line-placeholder', options);
                            } else {
                                stats.common++;
                                leftPanelHtml += createLineHtml(leftLineNum++, escapedLine, '', options);
                                rightPanelHtml += createLineHtml(rightLineNum++, escapedLine, '', options);
                            }
                        });
                    }
                });

                return {
                    leftHtml: leftPanelHtml,
                    rightHtml: rightPanelHtml,
                    leftNum: leftLineNum,
                    rightNum: rightLineNum,
                    chunkStats: stats
                };
            }

                diff.forEach((part, index) => {
                    if (part.processed) return; // Skip parts already handled by modification logic

                    const lines = part.value.split('\n');
                    if (lines[lines.length - 1] === '') lines.pop();

                    const isModification = part.removed && diff[index + 1]?.added;
                    if (isModification && options.wordLevelDiff) {
                        const nextPart = diff[index + 1];
                        const oldLines = lines;
                        const newLines = nextPart.value.split('\n');
                        if (newLines[newLines.length - 1] === '') newLines.pop();
                        
                        const maxLen = Math.max(oldLines.length, newLines.length);
                        stats.modified += maxLen;
                        
                        for(let i = 0; i < maxLen; i++) {
                            const oldLine = oldLines[i];
                            const newLine = newLines[i];

                            if (oldLine !== undefined && newLine !== undefined) {
                                // FIX: Use the correct Diff reference
                                const DiffLib = window.Diff || Diff || JsDiff;
                                let wordDiff;

                                if (DiffLib && DiffLib.diffWords) {
                                    wordDiff = DiffLib.diffWords(oldLine, newLine, options);
                                } else {
                                    // Basic word diff fallback
                                    wordDiff = basicDiffWords(oldLine, newLine);
                                }
                                let leftLineContent = '';
                                let rightLineContent = '';
                                wordDiff.forEach(wordPart => {
                                    const escapedContent = escapeHtml(wordPart.value);
                                    if (wordPart.added) {
                                        rightLineContent += `<span class="line-modified-word">${escapedContent}</span>`;
                                    } else if (wordPart.removed) {
                                        leftLineContent += `<span class="line-modified-word">${escapedContent}</span>`;
                                    } else {
                                        leftLineContent += escapedContent;
                                        rightLineContent += escapedContent;
                                    }
                                });
                                leftPanelHtml += createLineHtml(leftLineNum++, leftLineContent, 'line-removed', options);
                                rightPanelHtml += createLineHtml(rightLineNum++, rightLineContent, 'line-added', options);
                            } else if (oldLine !== undefined) {
                                leftPanelHtml += createLineHtml(leftLineNum++, escapeHtml(oldLine), 'line-removed', options);
                                rightPanelHtml += createLineHtml('&nbsp;', '', 'line-placeholder', options);
                            } else if (newLine !== undefined) {
                                leftPanelHtml += createLineHtml('&nbsp;', '', 'line-placeholder', options);
                                rightPanelHtml += createLineHtml(rightLineNum++, escapeHtml(newLine), 'line-added', options);
                            }
                        }
                        nextPart.processed = true;
                    } else {
                        lines.forEach(line => {
                            // Always show lines, even blank ones, but apply the showBlankLines option for styling
                            const escapedLine = escapeHtml(line);
                            if (part.added) {
                                stats.added++;
                                rightPanelHtml += createLineHtml(rightLineNum++, escapedLine, 'line-added', options);
                                leftPanelHtml += createLineHtml('&nbsp;', '', 'line-placeholder', options);
                            } else if (part.removed) {
                                stats.deleted++;
                                leftPanelHtml += createLineHtml(leftLineNum++, escapedLine, 'line-removed', options);
                                rightPanelHtml += createLineHtml('&nbsp;', '', 'line-placeholder', options);
                            } else {
                                stats.common++;
                                leftPanelHtml += createLineHtml(leftLineNum++, escapedLine, '', options);
                                rightPanelHtml += createLineHtml(rightLineNum++, escapedLine, '', options);
                            }
                        });
                    }
                });



            /**
             * Creates the HTML for a single line in the diff output.
             */
            function createLineHtml(num, content, typeClass, options) {
                try {
                    let lineClass = 'line';
                    if (typeClass) lineClass += ` ${typeClass}`;

                    // Safe content handling
                    const safeContent = content || '';
                    if (safeContent.trim() === '') lineClass += ' line-blank';

                    const numHtml = options.showLineNumbers ? `<div class="line-num">${num}</div>` : '';
                    return `<div class="${lineClass}">${numHtml}<div class="line-content">${safeContent || '&nbsp;'}</div></div>`;
                } catch (error) {
                    console.error('Error in createLineHtml:', error);
                    return `<div class="line"><div class="line-content">&nbsp;</div></div>`;
                }
            }
            
            /**
             * Updates the statistics display area with enhanced information.
             */
            function updateStats(stats) {
                const total = stats.added + stats.deleted + stats.common + stats.modified;
                const changePercentage = total > 0 ? Math.round(((stats.added + stats.deleted + stats.modified) / total) * 100) : 0;

                statsDisplay.innerHTML = `
                    <div class="flex items-center">
                        <span class="text-green-600 font-semibold">${stats.added}</span>
                        <span class="text-green-600 text-xs ml-1">Added</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-red-600 font-semibold">${stats.deleted}</span>
                        <span class="text-red-600 text-xs ml-1">Deleted</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-yellow-600 font-semibold">${stats.modified}</span>
                        <span class="text-yellow-600 text-xs ml-1">Modified</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-gray-600 font-semibold">${stats.common}</span>
                        <span class="text-gray-600 text-xs ml-1">Unchanged</span>
                    </div>
                    <div class="flex items-center col-span-2 md:col-span-4">
                        <span class="text-blue-600 font-semibold">${changePercentage}%</span>
                        <span class="text-blue-600 text-xs ml-1">Changed</span>
                        <span class="text-gray-500 text-xs ml-2">(${total} total lines)</span>
                    </div>
                `;
            }

            /**
             * Basic word diff fallback function.
             */
            function basicDiffWords(oldLine, newLine) {
                try {
                    const safeOldLine = oldLine || '';
                    const safeNewLine = newLine || '';

                    const oldWords = safeOldLine.split(/(\s+)/);
                    const newWords = safeNewLine.split(/(\s+)/);
                    const diff = [];

                    // Simple word comparison
                    const maxLength = Math.max(oldWords.length, newWords.length);
                    for (let i = 0; i < maxLength; i++) {
                        const oldWord = oldWords[i];
                        const newWord = newWords[i];

                        if (oldWord === newWord) {
                            if (oldWord !== undefined) {
                                diff.push({ value: oldWord });
                            }
                        } else {
                            if (oldWord !== undefined) {
                                diff.push({ removed: true, value: oldWord });
                            }
                            if (newWord !== undefined) {
                                diff.push({ added: true, value: newWord });
                            }
                        }
                    }

                    return diff;
                } catch (error) {
                    console.error('Error in basicDiffWords:', error);
                    return [{ value: oldLine || '' }, { value: newLine || '' }];
                }
            }

            /**
             * Basic diff fallback function when jsdiff library is not available.
             */
            function basicDiffLines(oldText, newText) {
                try {
                    const safeOldText = oldText || '';
                    const safeNewText = newText || '';

                    const oldLines = safeOldText.split('\n');
                    const newLines = safeNewText.split('\n');
                    const diff = [];

                    // Simple line-by-line comparison
                    const maxLength = Math.max(oldLines.length, newLines.length);
                    let oldIndex = 0;
                    let newIndex = 0;

                    while (oldIndex < oldLines.length || newIndex < newLines.length) {
                        const oldLine = oldLines[oldIndex];
                        const newLine = newLines[newIndex];

                        if (oldIndex >= oldLines.length) {
                            // Only new lines left
                            diff.push({ added: true, value: (newLine || '') + '\n' });
                            newIndex++;
                        } else if (newIndex >= newLines.length) {
                            // Only old lines left
                            diff.push({ removed: true, value: (oldLine || '') + '\n' });
                            oldIndex++;
                        } else if (oldLine === newLine) {
                            // Lines are the same
                            diff.push({ value: (oldLine || '') + '\n' });
                            oldIndex++;
                            newIndex++;
                        } else {
                            // Lines are different - mark as removed and added
                            diff.push({ removed: true, value: (oldLine || '') + '\n' });
                            diff.push({ added: true, value: (newLine || '') + '\n' });
                            oldIndex++;
                            newIndex++;
                        }
                    }

                    return diff;
                } catch (error) {
                    console.error('Error in basicDiffLines:', error);
                    return [
                        { removed: true, value: oldText || '' },
                        { added: true, value: newText || '' }
                    ];
                }
            }

            /**
             * Handles keyboard shortcuts.
             */
            function handleKeyboardShortcuts(event) {
                // Ctrl/Cmd + Enter: Compare
                if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                    event.preventDefault();
                    runComparison();
                }

                // Ctrl/Cmd + R: Clear all
                if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
                    event.preventDefault();
                    clearAll();
                }

                // Ctrl/Cmd + C: Copy diff (when results are visible)
                if ((event.ctrlKey || event.metaKey) && event.key === 'c' && !resultsContainer.classList.contains('hidden')) {
                    if (event.target.tagName !== 'TEXTAREA' && event.target.tagName !== 'INPUT') {
                        event.preventDefault();
                        copyDiffToClipboard();
                    }
                }

                // Escape: Clear all
                if (event.key === 'Escape') {
                    clearAll();
                }
            }

            /**
             * Sets up drag and drop functionality.
             */
            function setupDragAndDrop() {
                [textInput1, textInput2].forEach((textarea, index) => {
                    const panelNumber = index + 1;

                    textarea.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        textarea.classList.add('border-blue-400', 'bg-blue-50');
                    });

                    textarea.addEventListener('dragleave', (e) => {
                        e.preventDefault();
                        textarea.classList.remove('border-blue-400', 'bg-blue-50');
                    });

                    textarea.addEventListener('drop', (e) => {
                        e.preventDefault();
                        textarea.classList.remove('border-blue-400', 'bg-blue-50');

                        const files = e.dataTransfer.files;
                        if (files.length > 0) {
                            const file = files[0];
                            // Create a fake event object for handleFileUpload
                            const fakeEvent = { target: { files: [file] } };
                            handleFileUpload(fakeEvent, textarea, panelNumber);
                        }
                    });
                });
            }

            /**
             * Escapes HTML special characters to prevent XSS and rendering issues.
             */
            function escapeHtml(str) {
                if (str === null || str === undefined) {
                    return '';
                }
                return String(str)
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");
            }
        });
    </script>
</body>
</html>
