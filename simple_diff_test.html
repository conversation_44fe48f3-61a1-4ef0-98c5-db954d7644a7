<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Diff Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/diff@5.1.0/dist/diff.min.js"></script>
</head>
<body class="p-8 bg-gray-50">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">Simple Diff Test</h1>
        
        <div class="grid grid-cols-2 gap-4 mb-4">
            <div>
                <label class="block text-sm font-medium mb-2">Original Text</label>
                <textarea id="text1" class="w-full h-32 p-3 border rounded" placeholder="Enter original text...">Hello World
This is line 2
Common line</textarea>
            </div>
            <div>
                <label class="block text-sm font-medium mb-2">Changed Text</label>
                <textarea id="text2" class="w-full h-32 p-3 border rounded" placeholder="Enter changed text...">Hello Universe
This is line 2 modified
Common line
New line</textarea>
            </div>
        </div>
        
        <button id="compareBtn" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
            Compare
        </button>
        
        <div id="results" class="mt-6 grid grid-cols-2 gap-4 hidden">
            <div>
                <h3 class="font-semibold mb-2">Original</h3>
                <div id="output1" class="bg-white p-4 border rounded font-mono text-sm"></div>
            </div>
            <div>
                <h3 class="font-semibold mb-2">Changed</h3>
                <div id="output2" class="bg-white p-4 border rounded font-mono text-sm"></div>
            </div>
        </div>
        
        <div id="debug" class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
            <h3 class="font-semibold mb-2">Debug Info</h3>
            <div id="debugOutput"></div>
        </div>
    </div>

    <script>
        const text1 = document.getElementById('text1');
        const text2 = document.getElementById('text2');
        const compareBtn = document.getElementById('compareBtn');
        const results = document.getElementById('results');
        const output1 = document.getElementById('output1');
        const output2 = document.getElementById('output2');
        const debugOutput = document.getElementById('debugOutput');
        
        function debug(message) {
            console.log(message);
            debugOutput.innerHTML += `<div>${message}</div>`;
        }
        
        compareBtn.addEventListener('click', () => {
            debug('Compare button clicked');
            debugOutput.innerHTML = '';
            
            try {
                const originalText = text1.value;
                const changedText = text2.value;
                
                debug(`Original text length: ${originalText.length}`);
                debug(`Changed text length: ${changedText.length}`);
                
                if (typeof window.Diff !== 'undefined') {
                    debug('Using window.Diff library');
                    const diff = window.Diff.diffLines(originalText, changedText);
                    debug(`Diff result: ${JSON.stringify(diff, null, 2)}`);
                    
                    renderSimpleDiff(diff);
                } else {
                    debug('window.Diff not available, using basic diff');
                    const diff = basicDiff(originalText, changedText);
                    renderSimpleDiff(diff);
                }
                
                results.classList.remove('hidden');
                
            } catch (error) {
                debug(`Error: ${error.message}`);
                debug(`Stack: ${error.stack}`);
            }
        });
        
        function basicDiff(oldText, newText) {
            const oldLines = oldText.split('\n');
            const newLines = newText.split('\n');
            const diff = [];
            
            const maxLen = Math.max(oldLines.length, newLines.length);
            for (let i = 0; i < maxLen; i++) {
                const oldLine = oldLines[i];
                const newLine = newLines[i];
                
                if (oldLine === newLine) {
                    if (oldLine !== undefined) {
                        diff.push({ value: oldLine + '\n' });
                    }
                } else {
                    if (oldLine !== undefined) {
                        diff.push({ removed: true, value: oldLine + '\n' });
                    }
                    if (newLine !== undefined) {
                        diff.push({ added: true, value: newLine + '\n' });
                    }
                }
            }
            
            return diff;
        }
        
        function renderSimpleDiff(diff) {
            let leftHtml = '';
            let rightHtml = '';
            
            diff.forEach(part => {
                const lines = part.value.split('\n');
                if (lines[lines.length - 1] === '') lines.pop();
                
                lines.forEach(line => {
                    const escapedLine = escapeHtml(line);
                    
                    if (part.added) {
                        rightHtml += `<div class="bg-green-100 p-1">+ ${escapedLine}</div>`;
                        leftHtml += `<div class="bg-gray-100 p-1">&nbsp;</div>`;
                    } else if (part.removed) {
                        leftHtml += `<div class="bg-red-100 p-1">- ${escapedLine}</div>`;
                        rightHtml += `<div class="bg-gray-100 p-1">&nbsp;</div>`;
                    } else {
                        leftHtml += `<div class="p-1">${escapedLine}</div>`;
                        rightHtml += `<div class="p-1">${escapedLine}</div>`;
                    }
                });
            });
            
            output1.innerHTML = leftHtml;
            output2.innerHTML = rightHtml;
        }
        
        function escapeHtml(str) {
            return (str || '')
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }
        
        // Test library loading
        setTimeout(() => {
            if (typeof window.Diff !== 'undefined') {
                debug('✓ Diff library loaded successfully');
            } else {
                debug('✗ Diff library not loaded');
            }
        }, 1000);
    </script>
</body>
</html>
